import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StatusBar } from 'react-native';
import { StyleSheet, Text, View } from 'react-native';
import { Login } from './navigation/screens/Login';
import { useEffect, useState } from 'react';
import { User } from 'firebase/auth';
import { FIREBASE_AUTH } from '../FirebaseConfig';
import { Home } from './navigation/screens/Home';

const AppStack = createNativeStackNavigator();
const RootStack = createNativeStackNavigator();
const AppTabs = createBottomTabNavigator();

export function App() {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    FIREBASE_AUTH.onAuthStateChanged((user) => {
      console.log('user', user);
      setUser(user);
    });
  }, []);

  return (
    <NavigationContainer>
      <AppStack.Navigator initialRouteName="Login">
        {user ? (
          <AppStack.Screen name="Home" component={Home} options={{ headerShown: false }} />
        ) : (
          <AppStack.Screen name="Login" component={Login} options={{ headerShown: false }} />
        )}
      </AppStack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
