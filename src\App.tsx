import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useEffect, useState } from 'react';
import { User } from 'firebase/auth';
import { FIREBASE_AUTH } from '../FirebaseConfig';
import { Login } from './navigation/screens/Login';
import { Navigation } from './navigation';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

// Import des écrans de l'application
import { Home } from './navigation/screens/Home';
import { Updates } from './navigation/screens/Updates';
import { Agenda } from './navigation/screens/Agenda';
import { Drive } from './navigation/screens/Drive';
import { Profile } from './navigation/screens/Profile';
import { Settings } from './navigation/screens/Settings';

const AuthStack = createNativeStackNavigator({
  screens: {
    Login: {
      screen: Login,
      options: {
        title: 'Login',
        headerShown: false,
      },
    },
  },
  initialRouteName: 'Login',
});

// Navigation par onglets en bas
const AppTabs = createBottomTabNavigator({
  screens: {
    Home: { screen: Home, options: { title: 'Feed' } },
    Updates: { screen: Updates, options: { title: 'Notifications' } },
    Agenda: { screen: Agenda },
    Drive: { screen: Drive },
  },
});

// Navigation par pile (stack) pour les écrans modaux
const RootStack = createNativeStackNavigator({
  screens: {
    AppTabs: { screen: AppTabs, options: { title: 'Home', headerShown: false } },
    Profile: { screen: Profile },
    Settings: { screen: Settings, options: { presentation: 'modal' } },
  },
});

export function App() {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    FIREBASE_AUTH.onAuthStateChanged((user) => {
      console.log('user', user);
      setUser(user);
    });
  }, []);

  if (user) {
    // User is logged in, use the complete navigation
    return <Navigation />;
  }

  // User is not logged in, show login screen
  return (
    <NavigationContainer>
      <AuthStack.Navigator>
        <AuthStack.Screen name="Login" component={Login} options={{ headerShown: false }} />
      </AuthStack.Navigator>
    </NavigationContainer>
  );
}
