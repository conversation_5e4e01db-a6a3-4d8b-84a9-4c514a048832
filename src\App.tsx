import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useEffect, useState } from 'react';
import { User } from 'firebase/auth';
import { FIREBASE_AUTH } from '../FirebaseConfig';
import { Login } from './navigation/screens/Login';
import { Navigation } from './navigation';

const AuthStack = createNativeStackNavigator();

export function App() {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    FIREBASE_AUTH.onAuthStateChanged((user) => {
      console.log('user', user);
      setUser(user);
    });
  }, []);

  if (user) {
    // User is logged in, use the complete navigation
    return <Navigation />;
  }

  // User is not logged in, show login screen
  return (
    <NavigationContainer>
      <AuthStack.Navigator>
        <AuthStack.Screen name="Login" component={Login} options={{ headerShown: false }} />
      </AuthStack.Navigator>
    </NavigationContainer>
  );
}
