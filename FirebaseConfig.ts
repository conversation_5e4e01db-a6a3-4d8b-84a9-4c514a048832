import { initializeApp } from 'firebase/app';
import { getAnalytics } from 'firebase/analytics';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

const firebaseConfig = {
  apiKey: 'AIzaSyAHSwwzRKcwvyjaJjsI-JmpY8Oxz55tw7A',
  authDomain: 'prototype-reactnative-36322.firebaseapp.com',
  projectId: 'prototype-reactnative-36322',
  storageBucket: 'prototype-reactnative-36322.firebasestorage.app',
  messagingSenderId: '975670785554',
  appId: '1:975670785554:web:ec23d8519fd5c9792a4acb',
  measurementId: 'G-MDY3T27EKZ',
};

export const FIREBASE_APP = initializeApp(firebaseConfig);
export const FIREBASE_AUTH = getAuth(FIREBASE_APP);
export const FIREBASE_DB = getFirestore(FIREBASE_APP);
const analytics = getAnalytics(FIREBASE_APP);
