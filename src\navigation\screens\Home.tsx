import { Button, Text } from '@react-navigation/elements';
import { StyleSheet, View } from 'react-native';
import { FIREBASE_AUTH } from '../../../FirebaseConfig';
import { User } from 'firebase/auth';

export function Home() {
  const user = FIREBASE_AUTH.currentUser as User;
  return (
    <View style={styles.container}>
      <Text>Home Screen</Text>
      <Text>Open up 'src/App.tsx' to start working on your app!</Text>
      <Button screen="Profile">Go to Profile</Button>
      <Button screen="Settings">Go to Settings</Button>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
});
